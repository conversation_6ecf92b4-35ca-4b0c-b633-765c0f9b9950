import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

export async function POST(request: Request) {
  try {
    const { email, password } = await request.json();
    
    // Call BetterAuth API
    const response = await fetch('https://api.betterauth.com/v1/token', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ email, password }),
    });

    if (!response.ok) {
      throw new Error('Login gagal. Periksa kembali email dan password Anda.');
    }

    const { token } = await response.json();
    
    // Set httpOnly cookie
    cookies().set('auth_token', token, {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      maxAge: 60 * 60 * 24 * 7, // 1 week
      path: '/',
      domain: '.monetizr.com',
    });

    return NextResponse.json({ success: true });
  } catch (error: any) {
    return NextResponse.json(
      { error: error.message || '<PERSON><PERSON><PERSON><PERSON> k<PERSON><PERSON> saat login' },
      { status: 401 }
    );
  }
}