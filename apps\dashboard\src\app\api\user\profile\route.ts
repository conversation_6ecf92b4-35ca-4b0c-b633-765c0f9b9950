import { NextResponse } from 'next/server';
import { cookies } from 'next/headers';

// Mock user data - in a real application, this would come from a database
let userProfile = {
  name: '<PERSON>',
  email: '<EMAIL>',
  bio: 'A passionate software developer.',
};

async function verifyAuthToken(token: string | undefined) {
  if (!token) {
    return null;
  }
  // In a real app, you'd verify the token with your auth provider (e.g., BetterAuth)
  // For this mock, we'll just assume the token is valid if it exists.
  // const response = await fetch('https://api.betterauth.com/v1/userinfo', {
  //   headers: { 'Authorization': `Bearer ${token}` }
  // });
  // if (!response.ok) return null;
  // return await response.json();
  return { id: 'user-123', email: userProfile.email };
}

export async function GET(request: Request) {
  const token = cookies().get('auth_token')?.value;
  const user = await verifyAuthToken(token);

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Fetch user profile from DB based on user.id
  return NextResponse.json(userProfile);
}

export async function PUT(request: Request) {
  const token = cookies().get('auth_token')?.value;
  const user = await verifyAuthToken(token);

  if (!user) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  try {
    const { name, bio } = await request.json();

    // Update user profile in the database
    userProfile.name = name;
    userProfile.bio = bio;

    return NextResponse.json({ success: true, user: userProfile });
  } catch (error) {
    return NextResponse.json({ error: 'Failed to update profile' }, { status: 500 });
  }
}